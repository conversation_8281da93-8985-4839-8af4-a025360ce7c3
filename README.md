# HyperLocal Dashboard - Store Performance Visualization

A comprehensive dashboard for visualizing store performance across different cities with interactive maps and detailed analytics.

## 🚀 Features

- **Interactive Map View**: Visualize store locations with color-coded performance indicators
- **City Selection**: Switch between different cities (Bengaluru, Delhi, Mumbai)
- **Performance Filtering**: Filter stores by performance categories (Good, Average, Poor, No Sales)
- **Store Search**: Search stores by name or address
- **Detailed Analytics**: View comprehensive store metrics including sales, growth, conversion rates
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## 🛠 Technology Stack

### Backend
- **Django 4.2.7** - Web framework
- **Django REST Framework** - API development
- **SQLite** - Database (easily configurable to PostgreSQL)
- **Django CORS Headers** - Cross-origin resource sharing
- **Python Decouple** - Environment variable management

### Frontend
- **React 18** - UI framework
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **Tailwind CSS** - Styling
- **React Query** - Data fetching and caching
- **React Leaflet** - Interactive maps
- **Radix UI** - Component library

## 📋 Prerequisites

- Python 3.8+
- Node.js 16+
- npm or yarn

## 🚀 Quick Start

### 1. Clone the Repository
```bash
git clone <repository-url>
cd HyperLocalDashboard
```

### 2. Backend Setup (Django)

#### Option A: Automated Setup
```bash
chmod +x start_django.sh
./start_django.sh
```

#### Option B: Manual Setup
```bash
# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Run migrations
python manage.py makemigrations
python manage.py migrate

# Seed database with sample data
python manage.py seed_stores

# Start Django server
python manage.py runserver 0.0.0.0:8000
```

### 3. Frontend Setup (React)
```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

### 4. Access the Application
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:8000/api/
- **Django Admin**: http://localhost:8000/admin/

## 📁 Project Structure

```
HyperLocalDashboard/
├── backend/                 # Django configuration
│   ├── settings.py         # Django settings
│   ├── urls.py            # Main URL configuration
│   └── wsgi.py            # WSGI configuration
├── stores/                 # Django app for store management
│   ├── models.py          # Store data model
│   ├── views.py           # API views
│   ├── serializers.py     # API serializers
│   ├── urls.py            # Store app URLs
│   └── management/        # Custom management commands
├── client/                # React frontend
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── lib/          # Utilities and API functions
│   │   └── pages/        # Page components
│   └── .env              # Frontend environment variables
├── venv/                  # Python virtual environment
├── requirements.txt       # Python dependencies
├── .env                   # Backend environment variables
└── manage.py             # Django management script
```

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```env
DEBUG=True
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///db.sqlite3
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173
```

#### Frontend (client/.env)
```env
VITE_API_URL=http://localhost:8000
```

## 📊 API Endpoints

### Cities
- `GET /api/cities/` - Get all available cities

### Stores
- `GET /api/stores/` - Get all stores
- `GET /api/stores/?city=bengaluru` - Get stores by city
- `GET /api/stores/{id}/` - Get specific store details

## 🎯 Usage

1. **Select a City**: Use the dropdown in the header to select a city
2. **View Store Performance**: Stores are color-coded on the map:
   - 🟢 Green: Good performance
   - 🟡 Yellow: Average performance
   - 🔴 Red: Poor performance
   - ⚪ Gray: No sales
3. **Filter Stores**: Use performance badges to filter stores by category
4. **Search Stores**: Use the search bar to find specific stores
5. **View Details**: Click on any store (map marker or list item) to see detailed information

## 🔄 Development Commands

```bash
# Backend commands
npm run dev:backend          # Start Django server
npm run migrate             # Run database migrations
npm run seed               # Seed database with sample data

# Frontend commands
npm run dev:frontend        # Start React dev server
npm run build              # Build for production

# Combined
npm run dev                # Start frontend (backend needs to be started separately)
```

## 🚀 Deployment

### Backend (Django)
1. Set `DEBUG=False` in production
2. Configure proper database (PostgreSQL recommended)
3. Set up static file serving
4. Use gunicorn for production server

### Frontend (React)
1. Build the application: `npm run build`
2. Serve the `dist` folder using a web server
3. Update `VITE_API_URL` to point to production API

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure Django CORS settings include your frontend URL
2. **API Connection Issues**: Verify backend is running on port 8000
3. **Map Not Loading**: Check internet connection for map tiles
4. **Database Issues**: Run migrations and seed data

### Support

For issues and questions, please create an issue in the repository.
