#!/bin/bash

# Create virtual environment
echo "Creating virtual environment..."
python3 -m venv venv

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Create Django project
echo "Creating Django project..."
django-admin startproject backend .

# Create Django app
echo "Creating Django app..."
cd backend
python manage.py startapp stores
cd ..

echo "Setup complete! To activate the virtual environment, run:"
echo "source venv/bin/activate"
