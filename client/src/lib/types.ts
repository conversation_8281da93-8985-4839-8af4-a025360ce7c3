export type PerformanceType = 'good' | 'average' | 'poor' | 'none';

export interface PerformanceStats {
  good: number;
  average: number;
  poor: number;
  none: number;
}

export const PERFORMANCE_COLORS: Record<PerformanceType, string> = {
  good: '#4CAF50',
  average: '#FF9800',
  poor: '#F44336',
  none: '#9E9E9E'
};

export const PERFORMANCE_LABELS: Record<PerformanceType, string> = {
  good: 'Good Sales',
  average: 'Average',
  poor: 'Poor',
  none: 'No Sales'
};
