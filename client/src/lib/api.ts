// API configuration for Django backend
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

export interface DjangoStore {
  id: number;
  name: string;
  address: string;
  city: string;
  pincode: string;
  latitude: string;
  longitude: string;
  manager: string;
  phone: string;
  performance: 'good' | 'average' | 'poor' | 'none';
  sales: string;
  growth: string;
  impressions: number;
  conversion_rate: string;
  market_share: string;
  last_updated: string;
  created_at: string;
}

export interface Store {
  id: number;
  name: string;
  address: string;
  city: string;
  pincode: string;
  latitude: string;
  longitude: string;
  manager: string;
  phone: string;
  performance: 'good' | 'average' | 'poor' | 'none';
  sales: string;
  growth: string;
  impressions: number;
  conversionRate: string;
  marketShare: string;
  lastUpdated: string;
  createdAt: string;
}

// Convert Django API response to frontend format
export function convertDjangoStore(djangoStore: DjangoStore): Store {
  return {
    id: djangoStore.id,
    name: djangoStore.name,
    address: djangoStore.address,
    city: djangoStore.city,
    pincode: djangoStore.pincode,
    latitude: djangoStore.latitude,
    longitude: djangoStore.longitude,
    manager: djangoStore.manager,
    phone: djangoStore.phone,
    performance: djangoStore.performance,
    sales: djangoStore.sales,
    growth: djangoStore.growth,
    impressions: djangoStore.impressions,
    conversionRate: djangoStore.conversion_rate,
    marketShare: djangoStore.market_share,
    lastUpdated: djangoStore.last_updated,
    createdAt: djangoStore.created_at,
  };
}

// API functions
export async function fetchStores(city: string): Promise<Store[]> {
  const response = await fetch(`${API_BASE_URL}/api/stores/?city=${city}`);
  if (!response.ok) {
    throw new Error('Failed to fetch stores');
  }
  const djangoStores: DjangoStore[] = await response.json();
  return djangoStores.map(convertDjangoStore);
}

export async function fetchStore(id: number): Promise<Store> {
  const response = await fetch(`${API_BASE_URL}/api/stores/${id}/`);
  if (!response.ok) {
    throw new Error('Failed to fetch store');
  }
  const djangoStore: DjangoStore = await response.json();
  return convertDjangoStore(djangoStore);
}

export async function fetchCities(): Promise<string[]> {
  const response = await fetch(`${API_BASE_URL}/api/cities/`);
  if (!response.ok) {
    throw new Error('Failed to fetch cities');
  }
  return response.json();
}
