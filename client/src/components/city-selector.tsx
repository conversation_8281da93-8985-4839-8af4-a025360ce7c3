import { useQuery } from "@tanstack/react-query";
import { ChevronDown } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface CitySelectorProps {
  selectedCity: string;
  onCityChange: (city: string) => void;
}

export default function CitySelector({ selectedCity, onCityChange }: CitySelectorProps) {
  const { data: cities = [], isLoading } = useQuery({
    queryKey: ["/api/cities"],
    queryFn: async () => {
      const response = await fetch("/api/cities");
      if (!response.ok) {
        throw new Error("Failed to fetch cities");
      }
      return response.json() as Promise<string[]>;
    },
  });

  const cityDisplayNames: Record<string, string> = {
    bengaluru: "Bengaluru",
    delhi: "Delhi",
    mumbai: "Mumbai",
  };

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2">
        <div className="h-8 w-32 bg-gray-200 animate-pulse rounded"></div>
      </div>
    );
  }

  return (
    <Select value={selectedCity} onValueChange={onCityChange}>
      <SelectTrigger className="w-[140px] border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
        <SelectValue placeholder="Select city" />
        <ChevronDown className="h-4 w-4 opacity-50" />
      </SelectTrigger>
      <SelectContent>
        {cities.map((city) => (
          <SelectItem key={city} value={city}>
            {cityDisplayNames[city] || city}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
