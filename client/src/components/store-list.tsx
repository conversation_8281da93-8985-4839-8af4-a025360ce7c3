import { useState } from "react";
import { Search, ArrowUpDown } from "lucide-react";
import { Store } from "@/lib/api";
import { PerformanceStats, PERFORMANCE_LABELS, PerformanceType } from "@/lib/types";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";

interface StoreListProps {
  stores: Store[];
  performanceStats: PerformanceStats;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  performanceFilter: string | null;
  onPerformanceFilterChange: (filter: string | null) => void;
  onStoreSelect: (store: Store) => void;
  selectedStore: Store | null;
  isLoading: boolean;
}

type SortField = 'name' | 'performance' | 'sales';
type SortDirection = 'asc' | 'desc';

export default function StoreList({
  stores,
  performanceStats,
  searchQuery,
  onSearchChange,
  performanceFilter,
  onPerformanceFilterChange,
  onStoreSelect,
  selectedStore,
  isLoading,
}: StoreListProps) {
  const [sortField, setSortField] = useState<SortField>('name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const sortedStores = [...stores].sort((a, b) => {
    let aValue: string | number;
    let bValue: string | number;

    switch (sortField) {
      case 'name':
        aValue = a.name;
        bValue = b.name;
        break;
      case 'performance':
        const performanceOrder = { good: 3, average: 2, poor: 1, none: 0 };
        aValue = performanceOrder[a.performance as PerformanceType];
        bValue = performanceOrder[b.performance as PerformanceType];
        break;
      case 'sales':
        aValue = parseFloat(a.sales);
        bValue = parseFloat(b.sales);
        break;
      default:
        return 0;
    }

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  const formatCurrency = (amount: string) => {
    const num = parseFloat(amount);
    if (num >= 100000) {
      return `₹${(num / 100000).toFixed(1)}L`;
    }
    return `₹${(num / 1000).toFixed(0)}K`;
  };

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'good': return 'w-3 h-3 bg-green-500 rounded-full';
      case 'average': return 'w-3 h-3 bg-yellow-500 rounded-full';
      case 'poor': return 'w-3 h-3 bg-red-500 rounded-full';
      case 'none': return 'w-3 h-3 bg-gray-500 rounded-full';
      default: return 'w-3 h-3 bg-gray-500 rounded-full';
    }
  };

  const getPerformanceBadgeClass = (performance: string) => {
    switch (performance) {
      case 'good': return 'performance-badge-good';
      case 'average': return 'performance-badge-average';
      case 'poor': return 'performance-badge-poor';
      case 'none': return 'performance-badge-none';
      default: return 'performance-badge-none';
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col h-full bg-transparent">
        <div className="p-3 border-b border-gray-200/50 bg-white/80 backdrop-blur-sm">
          <div className="space-y-2">
            <Skeleton className="h-8 w-full bg-gray-200/50" />
            <div className="flex flex-wrap gap-1.5">
              {[...Array(4)].map((_, i) => (
                <Skeleton key={i} className="h-6 w-16 bg-gray-200/50" />
              ))}
            </div>
          </div>
        </div>
        <div className="flex-1 p-2 bg-white/60 backdrop-blur-sm">
          <div className="space-y-2">
            {[...Array(10)].map((_, i) => (
              <Skeleton key={i} className="h-10 w-full bg-gray-200/50" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-transparent">
      {/* Search and Filters - Extra Compact Header */}
      <div className="p-3 border-b border-gray-200/50 bg-white/80 backdrop-blur-sm">
        <div className="space-y-2">
          <div className="relative">
            <Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400 h-3.5 w-3.5" />
            <Input
              type="text"
              placeholder="Search stores..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-8 h-8 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/90 backdrop-blur-sm"
            />
          </div>

          {/* Performance Filter Badges - Extra Compact */}
          <div className="flex flex-wrap gap-1.5">
            {(Object.keys(performanceStats) as PerformanceType[]).map((performance) => (
              <Button
                key={performance}
                variant={performanceFilter === performance ? "default" : "outline"}
                size="sm"
                onClick={() =>
                  onPerformanceFilterChange(
                    performanceFilter === performance ? null : performance
                  )
                }
                className={`flex items-center space-x-1 text-xs px-1.5 py-0.5 h-6 ${
                  performanceFilter === performance
                    ? 'bg-blue-500 text-white hover:bg-blue-600 border-blue-500'
                    : 'hover:bg-gray-100 bg-white/80 backdrop-blur-sm'
                }`}
              >
                <div className={getPerformanceColor(performance).replace('w-3 h-3', 'w-2 h-2')} />
                <span className="hidden sm:inline text-xs">{PERFORMANCE_LABELS[performance]}</span>
                <span className="sm:hidden text-xs">{PERFORMANCE_LABELS[performance].slice(0, 3)}</span>
                <Badge variant="secondary" className="ml-0.5 text-xs px-1 py-0 h-3.5 min-w-[14px] bg-white/90">
                  {performanceStats[performance]}
                </Badge>
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Store List Table - Floating Style */}
      <div className="flex-1 overflow-auto bg-white/60 backdrop-blur-sm">
        <table className="w-full min-w-full">
          <thead className="bg-white/80 backdrop-blur-sm sticky top-0 z-10 border-b border-gray-200/50">
            <tr>
              <th className="px-2 sm:px-3 py-1.5 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleSort('name')}
                  className="p-0 h-auto font-medium text-xs uppercase tracking-wider text-gray-600 hover:bg-gray-100/50"
                >
                  Store Name
                  <ArrowUpDown className="ml-1 h-2.5 w-2.5" />
                </Button>
              </th>
              <th className="hidden sm:table-cell px-2 sm:px-3 py-1.5 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleSort('performance')}
                  className="p-0 h-auto font-medium text-xs uppercase tracking-wider text-gray-600 hover:bg-gray-100/50"
                >
                  Performance
                  <ArrowUpDown className="ml-1 h-2.5 w-2.5" />
                </Button>
              </th>
              <th className="px-2 sm:px-3 py-1.5 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleSort('sales')}
                  className="p-0 h-auto font-medium text-xs uppercase tracking-wider text-gray-600 hover:bg-gray-100/50"
                >
                  Sales
                  <ArrowUpDown className="ml-1 h-2.5 w-2.5" />
                </Button>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white/40 backdrop-blur-sm divide-y divide-gray-200/50">
            {sortedStores.map((store) => (
              <tr
                key={store.id}
                onClick={() => onStoreSelect(store)}
                className={`hover:bg-white/60 cursor-pointer transition-all duration-200 ${
                  selectedStore?.id === store.id ? 'bg-blue-50/80 ring-1 ring-blue-200' : ''
                }`}
              >
                <td className="px-2 sm:px-3 py-2">
                  <div className="flex items-center">
                    <div className={getPerformanceColor(store.performance).replace('w-3 h-3', 'w-2 h-2') + " mr-1.5 sm:mr-2 flex-shrink-0"} />
                    <div className="min-w-0 flex-1">
                      <div className="text-xs sm:text-sm font-medium text-gray-900 truncate">
                        {store.name}
                      </div>
                      <div className="text-xs text-gray-600 truncate">
                        {store.address}, {store.pincode}
                      </div>
                      {/* Show performance on mobile */}
                      <div className="sm:hidden mt-0.5">
                        <Badge className={getPerformanceBadgeClass(store.performance) + " text-xs px-1 py-0"}>
                          {store.performance === 'good' ? 'Good' :
                           store.performance === 'average' ? 'Avg' :
                           store.performance === 'poor' ? 'Poor' : 'None'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </td>
                <td className="hidden sm:table-cell px-2 sm:px-3 py-2 whitespace-nowrap">
                  <Badge className={getPerformanceBadgeClass(store.performance) + " text-xs px-1.5 py-0.5"}>
                    {store.performance === 'good' ? 'Good' :
                     store.performance === 'average' ? 'Average' :
                     store.performance === 'poor' ? 'Poor' : 'No Sales'}
                  </Badge>
                </td>
                <td className="px-2 sm:px-3 py-2 whitespace-nowrap">
                  <div className="text-xs sm:text-sm font-medium text-gray-900">
                    {formatCurrency(store.sales)}
                  </div>
                  <div className={`text-xs ${
                    store.growth.includes('+') ? 'text-green-600' :
                    store.growth.includes('-') ? 'text-red-600' : 'text-gray-500'
                  }`}>
                    {store.growth}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
