import { useState } from "react";
import { Search, ArrowUpDown } from "lucide-react";
import { Store } from "@/lib/api";
import { PerformanceStats, PERFORMANCE_LABELS, PerformanceType } from "@/lib/types";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";

interface StoreListProps {
  stores: Store[];
  performanceStats: PerformanceStats;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  performanceFilter: string | null;
  onPerformanceFilterChange: (filter: string | null) => void;
  onStoreSelect: (store: Store) => void;
  selectedStore: Store | null;
  isLoading: boolean;
}

type SortField = 'name' | 'performance' | 'sales';
type SortDirection = 'asc' | 'desc';

export default function StoreList({
  stores,
  performanceStats,
  searchQuery,
  onSearchChange,
  performanceFilter,
  onPerformanceFilterChange,
  onStoreSelect,
  selectedStore,
  isLoading,
}: StoreListProps) {
  const [sortField, setSortField] = useState<SortField>('name');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const sortedStores = [...stores].sort((a, b) => {
    let aValue: string | number;
    let bValue: string | number;

    switch (sortField) {
      case 'name':
        aValue = a.name;
        bValue = b.name;
        break;
      case 'performance':
        const performanceOrder = { good: 3, average: 2, poor: 1, none: 0 };
        aValue = performanceOrder[a.performance as PerformanceType];
        bValue = performanceOrder[b.performance as PerformanceType];
        break;
      case 'sales':
        aValue = parseFloat(a.sales);
        bValue = parseFloat(b.sales);
        break;
      default:
        return 0;
    }

    if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  const formatCurrency = (amount: string) => {
    const num = parseFloat(amount);
    if (num >= 100000) {
      return `₹${(num / 100000).toFixed(1)}L`;
    }
    return `₹${(num / 1000).toFixed(0)}K`;
  };

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'good': return 'w-3 h-3 bg-green-500 rounded-full';
      case 'average': return 'w-3 h-3 bg-yellow-500 rounded-full';
      case 'poor': return 'w-3 h-3 bg-red-500 rounded-full';
      case 'none': return 'w-3 h-3 bg-gray-500 rounded-full';
      default: return 'w-3 h-3 bg-gray-500 rounded-full';
    }
  };

  const getPerformanceBadgeClass = (performance: string) => {
    switch (performance) {
      case 'good': return 'performance-badge-good';
      case 'average': return 'performance-badge-average';
      case 'poor': return 'performance-badge-poor';
      case 'none': return 'performance-badge-none';
      default: return 'performance-badge-none';
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col h-full">
        <div className="p-6 border-b border-gray-200">
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <div className="flex space-x-2">
              {[...Array(4)].map((_, i) => (
                <Skeleton key={i} className="h-8 w-24" />
              ))}
            </div>
          </div>
        </div>
        <div className="flex-1 p-6">
          <div className="space-y-4">
            {[...Array(6)].map((_, i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Search and Filters */}
      <div className="p-6 border-b border-gray-200">
        <div className="space-y-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              type="text"
              placeholder="Search stores..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* Performance Filter Badges */}
          <div className="flex space-x-2">
            {(Object.keys(performanceStats) as PerformanceType[]).map((performance) => (
              <Button
                key={performance}
                variant={performanceFilter === performance ? "default" : "outline"}
                size="sm"
                onClick={() => 
                  onPerformanceFilterChange(
                    performanceFilter === performance ? null : performance
                  )
                }
                className={`flex items-center space-x-2 ${
                  performanceFilter === performance
                    ? 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                    : 'hover:bg-gray-100'
                }`}
              >
                <div className={getPerformanceColor(performance)} />
                <span>{PERFORMANCE_LABELS[performance]}</span>
                <Badge variant="secondary" className="ml-1">
                  {performanceStats[performance]}
                </Badge>
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Store List Table */}
      <div className="flex-1 overflow-auto">
        <table className="w-full min-w-full">
          <thead className="bg-gray-50 sticky top-0 z-10">
            <tr>
              <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleSort('name')}
                  className="p-0 h-auto font-medium text-xs uppercase tracking-wider text-gray-500 hover:bg-gray-100"
                >
                  Store Name
                  <ArrowUpDown className="ml-1 h-3 w-3" />
                </Button>
              </th>
              <th className="hidden sm:table-cell px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleSort('performance')}
                  className="p-0 h-auto font-medium text-xs uppercase tracking-wider text-gray-500 hover:bg-gray-100"
                >
                  Performance
                  <ArrowUpDown className="ml-1 h-3 w-3" />
                </Button>
              </th>
              <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleSort('sales')}
                  className="p-0 h-auto font-medium text-xs uppercase tracking-wider text-gray-500 hover:bg-gray-100"
                >
                  Sales
                  <ArrowUpDown className="ml-1 h-3 w-3" />
                </Button>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedStores.map((store) => (
              <tr
                key={store.id}
                onClick={() => onStoreSelect(store)}
                className={`hover:bg-gray-50 cursor-pointer transition-colors ${
                  selectedStore?.id === store.id ? 'bg-blue-50' : ''
                }`}
              >
                <td className="px-3 sm:px-6 py-4">
                  <div className="flex items-center">
                    <div className={getPerformanceColor(store.performance) + " mr-2 sm:mr-3"} />
                    <div className="min-w-0 flex-1">
                      <div className="text-sm font-medium text-gray-900 truncate">
                        {store.name}
                      </div>
                      <div className="text-xs sm:text-sm text-gray-500 truncate">
                        {store.address}, {store.pincode}
                      </div>
                      {/* Show performance on mobile */}
                      <div className="sm:hidden mt-1">
                        <Badge className={getPerformanceBadgeClass(store.performance)} size="sm">
                          {store.performance === 'good' ? 'Excellent' :
                           store.performance === 'average' ? 'Average' :
                           store.performance === 'poor' ? 'Poor' : 'No Sales'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </td>
                <td className="hidden sm:table-cell px-3 sm:px-6 py-4 whitespace-nowrap">
                  <Badge className={getPerformanceBadgeClass(store.performance)}>
                    {store.performance === 'good' ? 'Excellent' :
                     store.performance === 'average' ? 'Average' :
                     store.performance === 'poor' ? 'Poor' : 'No Sales'}
                  </Badge>
                </td>
                <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {formatCurrency(store.sales)}
                  </div>
                  <div className={`text-xs sm:text-sm ${
                    store.growth.includes('+') ? 'text-green-600' :
                    store.growth.includes('-') ? 'text-red-600' : 'text-gray-500'
                  }`}>
                    {store.growth}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
