import { useEffect, useRef } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, CircleMarker, <PERSON><PERSON>, useMap } from "react-leaflet";
import { Store } from "@/lib/api";
import { PERFORMANCE_COLORS, PERFORMANCE_LABELS, PerformanceType } from "@/lib/types";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ExpandIcon } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import "leaflet/dist/leaflet.css";

interface StoreMapProps {
  stores: Store[];
  selectedStore: Store | null;
  onStoreSelect: (store: Store) => void;
  isLoading: boolean;
}

// Component to update map view when stores change
function MapController({ stores }: { stores: Store[] }) {
  const map = useMap();

  useEffect(() => {
    if (stores.length > 0) {
      const bounds = stores.map(store => [
        parseFloat(store.latitude),
        parseFloat(store.longitude)
      ] as [number, number]);
      
      if (bounds.length === 1) {
        map.setView(bounds[0], 13);
      } else {
        map.fitBounds(bounds, { padding: [20, 20] });
      }
    }
  }, [stores, map]);

  return null;
}

export default function StoreMap({ stores, selectedStore, onStoreSelect, isLoading }: StoreMapProps) {
  const mapRef = useRef<any>(null);

  const formatCurrency = (amount: string) => {
    const num = parseFloat(amount);
    if (num >= 100000) {
      return `₹${(num / 100000).toFixed(1)}L`;
    }
    return `₹${(num / 1000).toFixed(0)}K`;
  };

  const getMarkerRadius = (store: Store) => {
    if (selectedStore?.id === store.id) return 12;
    return 8;
  };

  const getMarkerWeight = (store: Store) => {
    if (selectedStore?.id === store.id) return 4;
    return 2;
  };

  const resetMapView = () => {
    if (mapRef.current && stores.length > 0) {
      const bounds = stores.map(store => [
        parseFloat(store.latitude),
        parseFloat(store.longitude)
      ] as [number, number]);
      
      if (bounds.length === 1) {
        mapRef.current.setView(bounds[0], 13);
      } else {
        mapRef.current.fitBounds(bounds, { padding: [20, 20] });
      }
    }
  };

  if (isLoading) {
    return (
      <div className="w-full h-full relative">
        <Skeleton className="w-full h-full" />
        <div className="absolute top-4 right-4 z-10 space-y-2">
          <Skeleton className="h-32 w-24" />
          <Skeleton className="h-10 w-10" />
        </div>
      </div>
    );
  }

  const defaultCenter: [number, number] = stores.length > 0 
    ? [parseFloat(stores[0].latitude), parseFloat(stores[0].longitude)]
    : [12.9716, 77.5946]; // Default to Bengaluru

  return (
    <div className="w-full h-full relative">
      {/* Map Controls */}
      <div className="absolute top-4 right-4 z-[1000] space-y-2">
        <Card className="shadow-md">
          <CardContent className="p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Legend</h3>
            <div className="space-y-1">
              {(Object.keys(PERFORMANCE_COLORS) as PerformanceType[]).map((performance) => (
                <div key={performance} className="flex items-center space-x-2 text-xs">
                  <div 
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: PERFORMANCE_COLORS[performance] }}
                  />
                  <span>{PERFORMANCE_LABELS[performance]}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
        
        <Button
          size="sm"
          variant="outline"
          onClick={resetMapView}
          className="bg-white shadow-md hover:bg-gray-50"
          title="Reset View"
        >
          <ExpandIcon className="h-4 w-4" />
        </Button>
      </div>

      {/* Map Container */}
      <MapContainer
        center={defaultCenter}
        zoom={11}
        className="w-full h-full"
        ref={mapRef}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        
        <MapController stores={stores} />
        
        {stores.map((store) => (
          <CircleMarker
            key={store.id}
            center={[parseFloat(store.latitude), parseFloat(store.longitude)]}
            color={PERFORMANCE_COLORS[store.performance as PerformanceType]}
            fillColor={PERFORMANCE_COLORS[store.performance as PerformanceType]}
            fillOpacity={0.8}
            radius={getMarkerRadius(store)}
            weight={getMarkerWeight(store)}
            eventHandlers={{
              click: () => onStoreSelect(store),
            }}
          >
            <Popup>
              <div className="p-2 min-w-[200px]">
                <h3 className="font-medium text-gray-900 mb-1">{store.name}</h3>
                <p className="text-sm text-gray-600 mb-2">{store.address}, {store.pincode}</p>
                <p className="text-sm font-medium text-gray-900">
                  Sales: {formatCurrency(store.sales)}
                </p>
                <p className={`text-sm ${
                  store.growth.includes('+') ? 'text-green-600' :
                  store.growth.includes('-') ? 'text-red-600' : 'text-gray-500'
                }`}>
                  {store.growth}
                </p>
                <Button
                  size="sm"
                  className="mt-2 w-full"
                  onClick={() => onStoreSelect(store)}
                >
                  View Details
                </Button>
              </div>
            </Popup>
          </CircleMarker>
        ))}
      </MapContainer>
    </div>
  );
}
