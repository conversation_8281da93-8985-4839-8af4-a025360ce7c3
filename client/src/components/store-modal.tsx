import { X } from "lucide-react";
import { Store } from "@/lib/api";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface StoreModalProps {
  store: Store;
  onClose: () => void;
}

export default function StoreModal({ store, onClose }: StoreModalProps) {
  const formatCurrency = (amount: string) => {
    const num = parseFloat(amount);
    if (num >= 100000) {
      return `₹${(num / 100000).toFixed(1)}L`;
    }
    return `₹${(num / 1000).toFixed(0)}K`;
  };

  const getPerformanceBadgeClass = (performance: string) => {
    switch (performance) {
      case 'good': return 'performance-badge-good';
      case 'average': return 'performance-badge-average';
      case 'poor': return 'performance-badge-poor';
      case 'none': return 'performance-badge-none';
      default: return 'performance-badge-none';
    }
  };

  const getPerformanceLabel = (performance: string) => {
    switch (performance) {
      case 'good': return 'Excellent';
      case 'average': return 'Average';
      case 'poor': return 'Poor';
      case 'none': return 'No Sales';
      default: return 'Unknown';
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-medium text-gray-900">
            Store Details
          </DialogTitle>
        </DialogHeader>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-3">Store Information</h3>
            <div className="space-y-3">
              <div>
                <label className="text-xs text-gray-500">Store Name</label>
                <p className="text-sm font-medium">{store.name}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Address</label>
                <p className="text-sm">{store.address}, {store.pincode}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Store Manager</label>
                <p className="text-sm">{store.manager}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Phone</label>
                <p className="text-sm">{store.phone}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Performance Status</label>
                <div className="mt-1">
                  <Badge className={getPerformanceBadgeClass(store.performance)}>
                    {getPerformanceLabel(store.performance)}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-3">Performance Metrics</h3>
            <div className="space-y-3">
              <div>
                <label className="text-xs text-gray-500">Monthly Sales</label>
                <p className={`text-lg font-semibold ${
                  store.performance === 'good' ? 'text-green-600' :
                  store.performance === 'poor' ? 'text-red-600' : 'text-gray-900'
                }`}>
                  {formatCurrency(store.sales)}
                </p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Growth Rate</label>
                <p className={`text-sm ${
                  store.growth.includes('+') ? 'text-green-600' :
                  store.growth.includes('-') ? 'text-red-600' : 'text-gray-500'
                }`}>
                  {store.growth}
                </p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Impressions</label>
                <p className="text-sm">{store.impressions.toLocaleString()}</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Conversion Rate</label>
                <p className="text-sm">{store.conversionRate}%</p>
              </div>
              <div>
                <label className="text-xs text-gray-500">Market Share</label>
                <p className="text-sm">{store.marketShare}%</p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-6 pt-6 border-t border-gray-200">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Recent Activity</h3>
          <div className="space-y-2">
            <div className="flex items-center justify-between py-2 px-3 bg-green-50 rounded">
              <span className="text-sm">New promotion launched</span>
              <span className="text-xs text-gray-500">2 days ago</span>
            </div>
            <div className="flex items-center justify-between py-2 px-3 bg-blue-50 rounded">
              <span className="text-sm">Inventory updated</span>
              <span className="text-xs text-gray-500">3 days ago</span>
            </div>
            <div className="flex items-center justify-between py-2 px-3 bg-yellow-50 rounded">
              <span className="text-sm">Staff training completed</span>
              <span className="text-xs text-gray-500">1 week ago</span>
            </div>
          </div>
        </div>
        
        <div className="mt-6 flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            View Full Report
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
