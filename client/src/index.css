@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 20 14.3% 4.1%;
  --muted: 60 4.8% 95.9%;
  --muted-foreground: 25 5.3% 44.7%;
  --popover: 0 0% 100%;
  --popover-foreground: 20 14.3% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 20 14.3% 4.1%;
  --border: 20 5.9% 90%;
  --input: 20 5.9% 90%;
  --primary: 207 90% 54%;
  --primary-foreground: 211 100% 99%;
  --secondary: 60 4.8% 95.9%;
  --secondary-foreground: 24 9.8% 10%;
  --accent: 60 4.8% 95.9%;
  --accent-foreground: 24 9.8% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 60 9.1% 97.8%;
  --ring: 20 14.3% 4.1%;
  --radius: 0.5rem;
  
  /* Custom colors for store performance */
  --good-performance: 76 100% 50%; /* #4CAF50 */
  --average-performance: 36 100% 50%; /* #FF9800 */
  --poor-performance: 4 90% 58%; /* #F44336 */
  --no-sales: 0 0% 62%; /* #9E9E9E */
  
  /* Material Design Blue */
  --material-blue: 207 90% 54%; /* #1976D2 */
  --material-blue-dark: 208 100% 54%; /* #1565C0 */
}

.dark {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --primary: 207 90% 54%;
  --primary-foreground: 211 100% 99%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 240 4.9% 83.9%;
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Roboto', sans-serif;
  }
}

/* Custom styles for performance indicators */
.performance-good {
  color: hsl(var(--good-performance));
}

.performance-average {
  color: hsl(var(--average-performance));
}

.performance-poor {
  color: hsl(var(--poor-performance));
}

.performance-none {
  color: hsl(var(--no-sales));
}

.bg-performance-good {
  background-color: hsl(var(--good-performance));
}

.bg-performance-average {
  background-color: hsl(var(--average-performance));
}

.bg-performance-poor {
  background-color: hsl(var(--poor-performance));
}

.bg-performance-none {
  background-color: hsl(var(--no-sales));
}

/* Leaflet map container */
.leaflet-container {
  height: 100%;
  width: 100%;
  z-index: 0 !important;
  position: relative;
}

/* Ensure map tiles load properly */
.leaflet-tile-pane {
  z-index: 1;
}

/* Map controls should be above tiles but below UI elements */
.leaflet-control-container {
  z-index: 10;
}

/* Leaflet popups should be above map but below modals */
.leaflet-popup-pane {
  z-index: 20;
}

/* Ensure map doesn't interfere with UI elements */
.leaflet-container .leaflet-control-zoom {
  z-index: 15;
}

/* Fix for map attribution */
.leaflet-control-attribution {
  z-index: 10;
  background: rgba(255, 255, 255, 0.8) !important;
}

/* Performance badge styles */
.performance-badge-good {
  @apply bg-green-100 text-green-800;
}

.performance-badge-average {
  @apply bg-yellow-100 text-yellow-800;
}

.performance-badge-poor {
  @apply bg-red-100 text-red-800;
}

.performance-badge-none {
  @apply bg-gray-100 text-gray-800;
}

/* Responsive design improvements */
@media (max-width: 1024px) {
  .leaflet-container {
    min-height: 400px;
  }
}

@media (max-width: 768px) {
  .leaflet-container {
    min-height: 350px;
  }

  /* Adjust map controls for mobile */
  .leaflet-control-container .leaflet-top.leaflet-right {
    top: 10px;
    right: 10px;
  }
}

/* Ensure proper scrolling on mobile */
@media (max-width: 640px) {
  .leaflet-container {
    min-height: 300px;
  }
}

/* Fix for select dropdown z-index issues */
[data-radix-select-content] {
  z-index: 100 !important;
}

/* Ensure dialog overlay is above everything */
[data-radix-dialog-overlay] {
  z-index: 150 !important;
}

[data-radix-dialog-content] {
  z-index: 200 !important;
}
