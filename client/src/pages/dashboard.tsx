import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Clock } from "lucide-react";
import { Store } from "@shared/schema";
import { PerformanceStats } from "@/lib/types";
import CitySelector from "@/components/city-selector";
import StoreList from "@/components/store-list";
import StoreMap from "@/components/store-map";
import StoreModal from "@/components/store-modal";

export default function Dashboard() {
  const [selectedCity, setSelectedCity] = useState("bengaluru");
  const [selectedStore, setSelectedStore] = useState<Store | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [performanceFilter, setPerformanceFilter] = useState<string | null>(null);

  // Fetch stores for selected city
  const { data: stores = [], isLoading: storesLoading } = useQuery({
    queryKey: ["/api/stores", selectedCity],
    queryFn: async () => {
      const response = await fetch(`/api/stores?city=${selectedCity}`);
      if (!response.ok) {
        throw new Error("Failed to fetch stores");
      }
      return response.json() as Promise<Store[]>;
    },
  });

  // Calculate performance stats
  const performanceStats: PerformanceStats = stores.reduce(
    (stats, store) => {
      stats[store.performance as keyof PerformanceStats]++;
      return stats;
    },
    { good: 0, average: 0, poor: 0, none: 0 }
  );

  // Filter stores based on search and performance filter
  const filteredStores = stores.filter((store) => {
    const matchesSearch = store.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         store.address.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = !performanceFilter || store.performance === performanceFilter;
    return matchesSearch && matchesFilter;
  });

  const handleStoreSelect = (store: Store) => {
    setSelectedStore(store);
  };

  const handleStoreModalClose = () => {
    setSelectedStore(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-medium text-gray-900">
                Store Performance Dashboard
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <CitySelector
                selectedCity={selectedCity}
                onCityChange={setSelectedCity}
              />
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Clock className="h-4 w-4 text-gray-400" />
                <span>Last updated: 2 hours ago</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex h-[calc(100vh-80px)]">
        {/* Store List Sidebar */}
        <div className="w-1/2 bg-white border-r border-gray-200">
          <StoreList
            stores={filteredStores}
            performanceStats={performanceStats}
            searchQuery={searchQuery}
            onSearchChange={setSearchQuery}
            performanceFilter={performanceFilter}
            onPerformanceFilterChange={setPerformanceFilter}
            onStoreSelect={handleStoreSelect}
            selectedStore={selectedStore}
            isLoading={storesLoading}
          />
        </div>

        {/* Map Container */}
        <div className="w-1/2 relative">
          <StoreMap
            stores={filteredStores}
            selectedStore={selectedStore}
            onStoreSelect={handleStoreSelect}
            isLoading={storesLoading}
          />
        </div>
      </div>

      {/* Store Details Modal */}
      {selectedStore && (
        <StoreModal
          store={selectedStore}
          onClose={handleStoreModalClose}
        />
      )}
    </div>
  );
}
