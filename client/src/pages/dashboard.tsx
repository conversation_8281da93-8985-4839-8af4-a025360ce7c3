import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Clock } from "lucide-react";
import { Store, fetchStores } from "@/lib/api";
import { PerformanceStats } from "@/lib/types";
import CitySelector from "@/components/city-selector";
import StoreList from "@/components/store-list";
import StoreMap from "@/components/store-map";
import StoreModal from "@/components/store-modal";

export default function Dashboard() {
  const [selectedCity, setSelectedCity] = useState("bengaluru");
  const [selectedStore, setSelectedStore] = useState<Store | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [performanceFilter, setPerformanceFilter] = useState<string | null>(null);

  // Fetch stores for selected city
  const { data: stores = [], isLoading: storesLoading } = useQuery({
    queryKey: ["/api/stores", selectedCity],
    queryFn: () => fetchStores(selectedCity),
  });

  // Calculate performance stats
  const performanceStats: PerformanceStats = stores.reduce(
    (stats, store) => {
      stats[store.performance as keyof PerformanceStats]++;
      return stats;
    },
    { good: 0, average: 0, poor: 0, none: 0 }
  );

  // Filter stores based on search and performance filter
  const filteredStores = stores.filter((store) => {
    const matchesSearch = store.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         store.address.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = !performanceFilter || store.performance === performanceFilter;
    return matchesSearch && matchesFilter;
  });

  const handleStoreSelect = (store: Store) => {
    setSelectedStore(store);
  };

  const handleStoreModalClose = () => {
    setSelectedStore(null);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200 relative z-30 sticky top-0">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl sm:text-2xl font-medium text-gray-900">
                Store Performance Dashboard
              </h1>
            </div>
            <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
              <CitySelector
                selectedCity={selectedCity}
                onCityChange={setSelectedCity}
              />
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Clock className="h-4 w-4 text-gray-400" />
                <span className="hidden sm:inline">Last updated: 2 hours ago</span>
                <span className="sm:hidden">Updated: 2h ago</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content - Map as Background with Floating Table */}
      <div className="relative min-h-[calc(100vh-120px)] lg:h-[calc(100vh-120px)]">
        {/* Map Background - Full Width */}
        <div className="absolute inset-0 w-full h-full z-0">
          <StoreMap
            stores={filteredStores}
            selectedStore={selectedStore}
            onStoreSelect={handleStoreSelect}
            isLoading={storesLoading}
          />
        </div>

        {/* Floating Store List Panel */}
        <div className="absolute top-2 left-2 bottom-2 right-2 sm:top-4 sm:left-4 sm:bottom-4 sm:right-auto w-auto sm:w-full sm:max-w-2xl lg:max-w-3xl xl:max-w-4xl z-20 pointer-events-none">
          <div className="h-full bg-white/95 backdrop-blur-md rounded-lg sm:rounded-xl shadow-2xl border border-white/20 pointer-events-auto overflow-hidden">
            <StoreList
              stores={filteredStores}
              performanceStats={performanceStats}
              searchQuery={searchQuery}
              onSearchChange={setSearchQuery}
              performanceFilter={performanceFilter}
              onPerformanceFilterChange={setPerformanceFilter}
              onStoreSelect={handleStoreSelect}
              selectedStore={selectedStore}
              isLoading={storesLoading}
            />
          </div>
        </div>
      </div>

      {/* Store Details Modal */}
      {selectedStore && (
        <StoreModal
          store={selectedStore}
          onClose={handleStoreModalClose}
        />
      )}
    </div>
  );
}
