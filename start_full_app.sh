#!/bin/bash

echo "🚀 Starting HyperLocal Dashboard..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed. Please install Python 3.8+ and try again."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 16+ and try again."
    exit 1
fi

# Setup Backend
print_status "Setting up Django backend..."

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    print_status "Creating Python virtual environment..."
    python3 -m venv venv
    print_success "Virtual environment created"
else
    print_status "Virtual environment already exists"
fi

# Activate virtual environment and install dependencies
print_status "Installing Python dependencies..."
source venv/bin/activate
pip install -r requirements.txt > /dev/null 2>&1
print_success "Python dependencies installed"

# Run migrations
print_status "Running database migrations..."
python manage.py makemigrations > /dev/null 2>&1
python manage.py migrate > /dev/null 2>&1
print_success "Database migrations completed"

# Seed database
print_status "Seeding database with sample data..."
python manage.py seed_stores > /dev/null 2>&1
print_success "Database seeded with sample data"

# Setup Frontend
print_status "Setting up React frontend..."

# Install Node.js dependencies
if [ ! -d "node_modules" ]; then
    print_status "Installing Node.js dependencies..."
    npm install > /dev/null 2>&1
    print_success "Node.js dependencies installed"
else
    print_status "Node.js dependencies already installed"
fi

# Start servers
print_status "Starting servers..."

# Start Django backend in background
print_status "Starting Django backend server on http://localhost:8000..."
source venv/bin/activate
python manage.py runserver 0.0.0.0:8000 > /dev/null 2>&1 &
DJANGO_PID=$!

# Wait a moment for Django to start
sleep 3

# Check if Django is running
if curl -s http://localhost:8000/api/cities/ > /dev/null; then
    print_success "Django backend is running on http://localhost:8000"
else
    print_error "Failed to start Django backend"
    kill $DJANGO_PID 2>/dev/null
    exit 1
fi

# Start React frontend
print_status "Starting React frontend server on http://localhost:5173..."
npm run dev > /dev/null 2>&1 &
REACT_PID=$!

# Wait a moment for React to start
sleep 5

# Check if React is running
if curl -s http://localhost:5173 > /dev/null; then
    print_success "React frontend is running on http://localhost:5173"
else
    print_warning "React frontend may still be starting up..."
fi

print_success "🎉 HyperLocal Dashboard is ready!"
echo ""
echo "📱 Frontend: http://localhost:5173"
echo "🔧 Backend API: http://localhost:8000/api/"
echo "⚙️  Django Admin: http://localhost:8000/admin/"
echo ""
echo "Press Ctrl+C to stop all servers"

# Function to cleanup on exit
cleanup() {
    print_status "Stopping servers..."
    kill $DJANGO_PID 2>/dev/null
    kill $REACT_PID 2>/dev/null
    print_success "Servers stopped"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Keep script running
wait
