import { stores, type Store, type InsertStore } from "@shared/schema";

export interface IStorage {
  getStoresByCity(city: string): Promise<Store[]>;
  getStore(id: number): Promise<Store | undefined>;
  createStore(store: InsertStore): Promise<Store>;
  updateStore(id: number, store: Partial<InsertStore>): Promise<Store | undefined>;
  deleteStore(id: number): Promise<boolean>;
  getAllCities(): Promise<string[]>;
}

export class MemStorage implements IStorage {
  private stores: Map<number, Store>;
  private currentId: number;

  constructor() {
    this.stores = new Map();
    this.currentId = 1;
    this.initializeData();
  }

  private initializeData() {
    // Bengaluru stores
    const bengaluruStores: InsertStore[] = [
      {
        name: "MegaMart South Bengaluru",
        address: "123 Main Street, Koramangala",
        city: "bengaluru",
        pincode: "560034",
        latitude: "12.9344",
        longitude: "77.6147",
        manager: "<PERSON><PERSON>",
        phone: "+91 98765 43210",
        performance: "good",
        sales: "240000",
        growth: "+12% vs last month",
        impressions: 45230,
        conversionRate: "3.2",
        marketShare: "8.5"
      },
      {
        name: "SuperStore Electronic City",
        address: "456 Tech Park Road, Electronic City",
        city: "bengaluru",
        pincode: "560100",
        latitude: "12.8457",
        longitude: "77.6603",
        manager: "Priya Sharma",
        phone: "+91 98765 43211",
        performance: "average",
        sales: "180000",
        growth: "+3% vs last month",
        impressions: 32150,
        conversionRate: "2.8",
        marketShare: "6.2"
      },
      {
        name: "QuickMart Whitefield",
        address: "789 IT Plaza, Whitefield",
        city: "bengaluru",
        pincode: "560066",
        latitude: "12.9698",
        longitude: "77.7500",
        manager: "Amit Verma",
        phone: "+91 98765 43212",
        performance: "poor",
        sales: "90000",
        growth: "-8% vs last month",
        impressions: 18750,
        conversionRate: "1.5",
        marketShare: "3.1"
      },
      {
        name: "FreshMart Indiranagar",
        address: "321 Commercial Street, Indiranagar",
        city: "bengaluru",
        pincode: "560038",
        latitude: "12.9784",
        longitude: "77.6408",
        manager: "Sunita Rao",
        phone: "+91 98765 43213",
        performance: "good",
        sales: "210000",
        growth: "+18% vs last month",
        impressions: 39850,
        conversionRate: "3.5",
        marketShare: "7.8"
      },
      {
        name: "CityMart Marathahalli",
        address: "654 Ring Road, Marathahalli",
        city: "bengaluru",
        pincode: "560037",
        latitude: "12.9565",
        longitude: "77.6972",
        manager: "Ravi Kumar",
        phone: "+91 98765 43214",
        performance: "none",
        sales: "0",
        growth: "Store closed",
        impressions: 0,
        conversionRate: "0",
        marketShare: "0"
      },
      {
        name: "TechMart HSR Layout",
        address: "987 Service Road, HSR Layout",
        city: "bengaluru",
        pincode: "560102",
        latitude: "12.9082",
        longitude: "77.6476",
        manager: "Kavitha Reddy",
        phone: "+91 98765 43215",
        performance: "average",
        sales: "150000",
        growth: "+5% vs last month",
        impressions: 28940,
        conversionRate: "2.4",
        marketShare: "5.3"
      }
    ];

    // Delhi stores
    const delhiStores: InsertStore[] = [
      {
        name: "Capital Mall Connaught Place",
        address: "Block A, Connaught Place",
        city: "delhi",
        pincode: "110001",
        latitude: "28.6315",
        longitude: "77.2167",
        manager: "Vikram Singh",
        phone: "+91 98765 43216",
        performance: "good",
        sales: "320000",
        growth: "+15% vs last month",
        impressions: 52340,
        conversionRate: "4.1",
        marketShare: "12.3"
      },
      {
        name: "Metro Store Karol Bagh",
        address: "Main Market, Karol Bagh",
        city: "delhi",
        pincode: "110005",
        latitude: "28.6519",
        longitude: "77.1909",
        manager: "Neha Gupta",
        phone: "+91 98765 43217",
        performance: "average",
        sales: "195000",
        growth: "+7% vs last month",
        impressions: 35670,
        conversionRate: "2.9",
        marketShare: "7.1"
      },
      {
        name: "Digital Hub Lajpat Nagar",
        address: "Central Market, Lajpat Nagar",
        city: "delhi",
        pincode: "110024",
        latitude: "28.5677",
        longitude: "77.2436",
        manager: "Arjun Mehta",
        phone: "+91 98765 43218",
        performance: "poor",
        sales: "85000",
        growth: "-12% vs last month",
        impressions: 16890,
        conversionRate: "1.3",
        marketShare: "2.8"
      }
    ];

    // Mumbai stores
    const mumbaiStores: InsertStore[] = [
      {
        name: "Marine Drive Megastore",
        address: "Queen's Necklace Complex, Marine Drive",
        city: "mumbai",
        pincode: "400020",
        latitude: "18.9441",
        longitude: "72.8230",
        manager: "Rohit Iyer",
        phone: "+91 98765 43219",
        performance: "good",
        sales: "410000",
        growth: "+22% vs last month",
        impressions: 68450,
        conversionRate: "4.8",
        marketShare: "15.2"
      },
      {
        name: "Bandra West Plaza",
        address: "Linking Road, Bandra West",
        city: "mumbai",
        pincode: "400050",
        latitude: "19.0544",
        longitude: "72.8347",
        manager: "Ananya Joshi",
        phone: "+91 98765 43220",
        performance: "average",
        sales: "220000",
        growth: "+4% vs last month",
        impressions: 41230,
        conversionRate: "3.1",
        marketShare: "8.9"
      },
      {
        name: "Thane Station Store",
        address: "Platform Complex, Thane Station",
        city: "mumbai",
        pincode: "400601",
        latitude: "19.1972",
        longitude: "72.9569",
        manager: "Sanjay Patil",
        phone: "+91 98765 43221",
        performance: "poor",
        sales: "105000",
        growth: "-6% vs last month",
        impressions: 23180,
        conversionRate: "1.8",
        marketShare: "4.2"
      }
    ];

    // Add all stores
    [...bengaluruStores, ...delhiStores, ...mumbaiStores].forEach(store => {
      this.createStore(store);
    });
  }

  async getStoresByCity(city: string): Promise<Store[]> {
    return Array.from(this.stores.values()).filter(store => store.city === city);
  }

  async getStore(id: number): Promise<Store | undefined> {
    return this.stores.get(id);
  }

  async createStore(insertStore: InsertStore): Promise<Store> {
    const id = this.currentId++;
    const store: Store = {
      ...insertStore,
      id,
      lastUpdated: new Date(),
    };
    this.stores.set(id, store);
    return store;
  }

  async updateStore(id: number, updateData: Partial<InsertStore>): Promise<Store | undefined> {
    const existingStore = this.stores.get(id);
    if (!existingStore) {
      return undefined;
    }

    const updatedStore: Store = {
      ...existingStore,
      ...updateData,
      lastUpdated: new Date(),
    };
    this.stores.set(id, updatedStore);
    return updatedStore;
  }

  async deleteStore(id: number): Promise<boolean> {
    return this.stores.delete(id);
  }

  async getAllCities(): Promise<string[]> {
    const cities = new Set<string>();
    this.stores.forEach(store => cities.add(store.city));
    return Array.from(cities).sort();
  }
}

export const storage = new MemStorage();
