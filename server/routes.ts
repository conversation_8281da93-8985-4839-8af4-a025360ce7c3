import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { z } from "zod";

export async function registerRoutes(app: Express): Promise<Server> {
  // Get all cities
  app.get("/api/cities", async (req, res) => {
    try {
      const cities = await storage.getAllCities();
      res.json(cities);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch cities" });
    }
  });

  // Get stores by city
  app.get("/api/stores", async (req, res) => {
    try {
      const cityParam = req.query.city as string;
      if (!cityParam) {
        return res.status(400).json({ message: "City parameter is required" });
      }

      const stores = await storage.getStoresByCity(cityParam);
      res.json(stores);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch stores" });
    }
  });

  // Get individual store
  app.get("/api/stores/:id", async (req, res) => {
    try {
      const idParam = req.params.id;
      const id = parseInt(idParam);
      
      if (isNaN(id)) {
        return res.status(400).json({ message: "Invalid store ID" });
      }

      const store = await storage.getStore(id);
      if (!store) {
        return res.status(404).json({ message: "Store not found" });
      }

      res.json(store);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch store" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
