from django.core.management.base import BaseCommand
from stores.models import Store


class Command(BaseCommand):
    help = 'Seed the database with sample store data'

    def handle(self, *args, **options):
        # Clear existing data
        Store.objects.all().delete()
        
        # Bengaluru stores
        bengaluru_stores = [
            {
                'name': 'MegaMart South Bengaluru',
                'address': '123 Main Street, Koramangala',
                'city': 'bengaluru',
                'pincode': '560034',
                'latitude': '12.9344',
                'longitude': '77.6147',
                'manager': '<PERSON><PERSON>',
                'phone': '+91 98765 43210',
                'performance': 'good',
                'sales': '240000',
                'growth': '+12% vs last month',
                'impressions': 45230,
                'conversion_rate': '3.2',
                'market_share': '8.5'
            },
            {
                'name': 'SuperStore Electronic City',
                'address': '456 Tech Park Road, Electronic City',
                'city': 'bengaluru',
                'pincode': '560100',
                'latitude': '12.8457',
                'longitude': '77.6603',
                'manager': '<PERSON><PERSON>',
                'phone': '+91 98765 43211',
                'performance': 'average',
                'sales': '180000',
                'growth': '+3% vs last month',
                'impressions': 32150,
                'conversion_rate': '2.8',
                'market_share': '6.2'
            },
            {
                'name': 'QuickMart Whitefield',
                'address': '789 IT Plaza, Whitefield',
                'city': 'bengaluru',
                'pincode': '560066',
                'latitude': '12.9698',
                'longitude': '77.7500',
                'manager': 'Amit Verma',
                'phone': '+91 98765 43212',
                'performance': 'poor',
                'sales': '90000',
                'growth': '-8% vs last month',
                'impressions': 18750,
                'conversion_rate': '1.5',
                'market_share': '3.1'
            },
            {
                'name': 'FreshMart Indiranagar',
                'address': '321 Commercial Street, Indiranagar',
                'city': 'bengaluru',
                'pincode': '560038',
                'latitude': '12.9784',
                'longitude': '77.6408',
                'manager': 'Sunita Rao',
                'phone': '+91 98765 43213',
                'performance': 'good',
                'sales': '210000',
                'growth': '+18% vs last month',
                'impressions': 39850,
                'conversion_rate': '3.5',
                'market_share': '7.8'
            },
            {
                'name': 'CityMart Marathahalli',
                'address': '654 Ring Road, Marathahalli',
                'city': 'bengaluru',
                'pincode': '560037',
                'latitude': '12.9565',
                'longitude': '77.6972',
                'manager': 'Ravi Kumar',
                'phone': '+91 98765 43214',
                'performance': 'none',
                'sales': '0',
                'growth': 'Store closed',
                'impressions': 0,
                'conversion_rate': '0',
                'market_share': '0'
            },
            {
                'name': 'TechMart HSR Layout',
                'address': '987 Service Road, HSR Layout',
                'city': 'bengaluru',
                'pincode': '560102',
                'latitude': '12.9082',
                'longitude': '77.6476',
                'manager': 'Kavitha Reddy',
                'phone': '+91 98765 43215',
                'performance': 'average',
                'sales': '150000',
                'growth': '+5% vs last month',
                'impressions': 28940,
                'conversion_rate': '2.4',
                'market_share': '5.3'
            }
        ]

        # Delhi stores
        delhi_stores = [
            {
                'name': 'Capital Mall Connaught Place',
                'address': 'Block A, Connaught Place',
                'city': 'delhi',
                'pincode': '110001',
                'latitude': '28.6315',
                'longitude': '77.2167',
                'manager': 'Vikram Singh',
                'phone': '+91 98765 43216',
                'performance': 'good',
                'sales': '320000',
                'growth': '+15% vs last month',
                'impressions': 52340,
                'conversion_rate': '4.1',
                'market_share': '12.3'
            },
            {
                'name': 'Metro Store Karol Bagh',
                'address': 'Main Market, Karol Bagh',
                'city': 'delhi',
                'pincode': '110005',
                'latitude': '28.6519',
                'longitude': '77.1909',
                'manager': 'Neha Gupta',
                'phone': '+91 98765 43217',
                'performance': 'average',
                'sales': '195000',
                'growth': '+7% vs last month',
                'impressions': 35670,
                'conversion_rate': '2.9',
                'market_share': '7.1'
            },
            {
                'name': 'Digital Hub Lajpat Nagar',
                'address': 'Central Market, Lajpat Nagar',
                'city': 'delhi',
                'pincode': '110024',
                'latitude': '28.5677',
                'longitude': '77.2436',
                'manager': 'Arjun Mehta',
                'phone': '+91 98765 43218',
                'performance': 'poor',
                'sales': '85000',
                'growth': '-12% vs last month',
                'impressions': 16890,
                'conversion_rate': '1.3',
                'market_share': '2.8'
            }
        ]

        # Mumbai stores
        mumbai_stores = [
            {
                'name': 'Marine Drive Megastore',
                'address': 'Queen\'s Necklace Complex, Marine Drive',
                'city': 'mumbai',
                'pincode': '400020',
                'latitude': '18.9441',
                'longitude': '72.8230',
                'manager': 'Rohit Iyer',
                'phone': '+91 98765 43219',
                'performance': 'good',
                'sales': '410000',
                'growth': '+22% vs last month',
                'impressions': 68450,
                'conversion_rate': '4.8',
                'market_share': '15.2'
            },
            {
                'name': 'Bandra West Plaza',
                'address': 'Linking Road, Bandra West',
                'city': 'mumbai',
                'pincode': '400050',
                'latitude': '19.0544',
                'longitude': '72.8347',
                'manager': 'Ananya Joshi',
                'phone': '+91 98765 43220',
                'performance': 'average',
                'sales': '220000',
                'growth': '+4% vs last month',
                'impressions': 41230,
                'conversion_rate': '3.1',
                'market_share': '8.9'
            },
            {
                'name': 'Thane Station Store',
                'address': 'Platform Complex, Thane Station',
                'city': 'mumbai',
                'pincode': '400601',
                'latitude': '19.1972',
                'longitude': '72.9569',
                'manager': 'Sanjay Patil',
                'phone': '+91 98765 43221',
                'performance': 'poor',
                'sales': '105000',
                'growth': '-6% vs last month',
                'impressions': 23180,
                'conversion_rate': '1.8',
                'market_share': '4.2'
            }
        ]

        # Create all stores
        all_stores = bengaluru_stores + delhi_stores + mumbai_stores

        for store_data in all_stores:
            Store.objects.create(**store_data)

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {len(all_stores)} stores')
        )
