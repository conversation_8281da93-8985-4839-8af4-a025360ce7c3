from django.contrib import admin
from .models import Store


@admin.register(Store)
class StoreAdmin(admin.ModelAdmin):
    list_display = ['name', 'city', 'performance', 'sales', 'manager', 'last_updated']
    list_filter = ['city', 'performance', 'last_updated']
    search_fields = ['name', 'address', 'manager', 'city']
    readonly_fields = ['last_updated', 'created_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'address', 'city', 'pincode')
        }),
        ('Location', {
            'fields': ('latitude', 'longitude')
        }),
        ('Contact', {
            'fields': ('manager', 'phone')
        }),
        ('Performance', {
            'fields': ('performance', 'sales', 'growth', 'impressions', 'conversion_rate', 'market_share')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'last_updated'),
            'classes': ('collapse',)
        }),
    )
