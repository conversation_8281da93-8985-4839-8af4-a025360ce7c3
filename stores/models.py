from django.db import models


class Store(models.Model):
    PERFORMANCE_CHOICES = [
        ('good', 'Good'),
        ('average', 'Average'),
        ('poor', 'Poor'),
        ('none', 'None'),
    ]

    name = models.CharField(max_length=255)
    address = models.TextField()
    city = models.CharField(max_length=100)
    pincode = models.CharField(max_length=10)
    latitude = models.DecimalField(max_digits=10, decimal_places=8)
    longitude = models.DecimalField(max_digits=11, decimal_places=8)
    manager = models.CharField(max_length=255)
    phone = models.CharField(max_length=20)
    performance = models.CharField(max_length=10, choices=PERFORMANCE_CHOICES)
    sales = models.DecimalField(max_digits=10, decimal_places=2)
    growth = models.CharField(max_length=100)
    impressions = models.IntegerField()
    conversion_rate = models.DecimalField(max_digits=5, decimal_places=2)
    market_share = models.DecimalField(max_digits=5, decimal_places=2)
    last_updated = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return f"{self.name} - {self.city}"
