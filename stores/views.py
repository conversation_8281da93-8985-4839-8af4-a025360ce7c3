from rest_framework import generics, status
from rest_framework.decorators import api_view
from rest_framework.response import Response
from django.db.models import Q
from .models import Store
from .serializers import StoreSerializer, CitySerializer


class StoreListView(generics.ListAPIView):
    serializer_class = StoreSerializer

    def get_queryset(self):
        queryset = Store.objects.all()
        city = self.request.query_params.get('city', None)
        if city is not None:
            queryset = queryset.filter(city__iexact=city)
        return queryset


class StoreDetailView(generics.RetrieveAPIView):
    queryset = Store.objects.all()
    serializer_class = StoreSerializer


@api_view(['GET'])
def cities_list(request):
    """Get all unique cities from stores"""
    cities = Store.objects.values_list('city', flat=True).distinct().order_by('city')
    return Response(list(cities))
