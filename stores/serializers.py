from rest_framework import serializers
from .models import Store


class StoreSerializer(serializers.ModelSerializer):
    class Meta:
        model = Store
        fields = [
            'id', 'name', 'address', 'city', 'pincode', 'latitude', 'longitude',
            'manager', 'phone', 'performance', 'sales', 'growth', 'impressions',
            'conversion_rate', 'market_share', 'last_updated', 'created_at'
        ]
        read_only_fields = ['id', 'last_updated', 'created_at']


class CitySerializer(serializers.Serializer):
    city = serializers.CharField(max_length=100)
