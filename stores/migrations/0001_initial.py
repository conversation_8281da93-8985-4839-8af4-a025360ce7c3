# Generated by Django 4.2.7 on 2025-06-02 06:44

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Store',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=255)),
                ('address', models.TextField()),
                ('city', models.Char<PERSON>ield(max_length=100)),
                ('pincode', models.Char<PERSON>ield(max_length=10)),
                ('latitude', models.DecimalField(decimal_places=8, max_digits=10)),
                ('longitude', models.DecimalField(decimal_places=8, max_digits=11)),
                ('manager', models.Char<PERSON>ield(max_length=255)),
                ('phone', models.Char<PERSON>ield(max_length=20)),
                ('performance', models.Char<PERSON>ield(choices=[('good', 'Good'), ('average', 'Average'), ('poor', 'Poor'), ('none', 'None')], max_length=10)),
                ('sales', models.DecimalField(decimal_places=2, max_digits=10)),
                ('growth', models.CharField(max_length=100)),
                ('impressions', models.IntegerField()),
                ('conversion_rate', models.DecimalField(decimal_places=2, max_digits=5)),
                ('market_share', models.DecimalField(decimal_places=2, max_digits=5)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
    ]
