#!/bin/bash

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Run migrations
echo "Running migrations..."
python manage.py makemigrations
python manage.py migrate

# Seed database
echo "Seeding database..."
python manage.py seed_stores

# Create superuser (optional)
echo "Creating superuser (optional)..."
echo "You can skip this by pressing Ctrl+C"
python manage.py createsuperuser --noinput --username admin --email <EMAIL> || true

# Start development server
echo "Starting Django development server..."
python manage.py runserver 0.0.0.0:8000
