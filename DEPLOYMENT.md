# Deployment Guide

This guide covers different deployment options for the HyperLocal Dashboard.

## 🚀 Quick Development Setup

### Option 1: Automated Setup (Recommended)
```bash
chmod +x start_full_app.sh
./start_full_app.sh
```

### Option 2: Manual Setup
```bash
# Backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
python manage.py migrate
python manage.py seed_stores
python manage.py runserver 0.0.0.0:8000

# Frontend (in another terminal)
npm install
npm run dev
```

## 🐳 Docker Deployment

### Prerequisites
- Docker
- Docker Compose

### Steps
```bash
# Build and start services
docker-compose up --build

# Access the application
# Backend: http://localhost:8000
# Database: PostgreSQL on port 5432
```

### Environment Configuration
Copy `.env.production` to `.env` and update the values:
```env
DEBUG=False
SECRET_KEY=your-production-secret-key
DATABASE_URL=postgresql://username:password@localhost:5432/hyperlocal_dashboard
ALLOWED_HOSTS=yourdomain.com,localhost
CORS_ALLOWED_ORIGINS=https://yourdomain.com
```

## ☁️ Cloud Deployment

### Backend (Django) - Heroku
1. Install Heroku CLI
2. Create Heroku app:
   ```bash
   heroku create your-app-name
   ```
3. Add PostgreSQL addon:
   ```bash
   heroku addons:create heroku-postgresql:hobby-dev
   ```
4. Set environment variables:
   ```bash
   heroku config:set DEBUG=False
   heroku config:set SECRET_KEY=your-secret-key
   heroku config:set ALLOWED_HOSTS=your-app-name.herokuapp.com
   ```
5. Deploy:
   ```bash
   git push heroku main
   heroku run python manage.py migrate
   heroku run python manage.py seed_stores
   ```

### Frontend (React) - Vercel
1. Install Vercel CLI:
   ```bash
   npm i -g vercel
   ```
2. Build the frontend:
   ```bash
   cd client
   npm run build
   ```
3. Deploy:
   ```bash
   vercel --prod
   ```
4. Set environment variable in Vercel dashboard:
   - `VITE_API_URL`: Your backend URL

### Frontend (React) - Netlify
1. Build the frontend:
   ```bash
   npm run build
   ```
2. Deploy the `dist` folder to Netlify
3. Set environment variables in Netlify dashboard:
   - `VITE_API_URL`: Your backend URL

## 🔧 Production Configuration

### Django Settings
Update `backend/settings.py` for production:
```python
# Security settings
SECURE_BROWSER_XSS_FILTER = True
SECURE_CONTENT_TYPE_NOSNIFF = True
X_FRAME_OPTIONS = 'DENY'
SECURE_HSTS_SECONDS = 31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS = True
SECURE_HSTS_PRELOAD = True

# Database
DATABASES = {
    'default': dj_database_url.parse(os.environ.get('DATABASE_URL'))
}

# Static files
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'
```

### Frontend Configuration
Update `client/.env.production`:
```env
VITE_API_URL=https://your-backend-domain.com
```

## 📊 Monitoring and Maintenance

### Health Checks
- Backend: `GET /api/cities/` should return city list
- Frontend: Should load without console errors

### Database Backup
```bash
# PostgreSQL backup
pg_dump your_database > backup.sql

# SQLite backup
cp db.sqlite3 backup_$(date +%Y%m%d).sqlite3
```

### Log Monitoring
- Django logs: Check application logs for errors
- Frontend: Monitor browser console for JavaScript errors

## 🔒 Security Considerations

1. **Environment Variables**: Never commit `.env` files
2. **Secret Key**: Use a strong, unique secret key in production
3. **HTTPS**: Always use HTTPS in production
4. **Database**: Use strong passwords and restrict access
5. **CORS**: Configure CORS properly for your domain

## 🚨 Troubleshooting

### Common Issues

1. **CORS Errors**
   - Check `CORS_ALLOWED_ORIGINS` in Django settings
   - Ensure frontend URL is included

2. **Database Connection**
   - Verify `DATABASE_URL` format
   - Check database server is running

3. **Static Files Not Loading**
   - Run `python manage.py collectstatic`
   - Check `STATIC_ROOT` and `STATIC_URL` settings

4. **Frontend API Calls Failing**
   - Verify `VITE_API_URL` is correct
   - Check network connectivity

### Performance Optimization

1. **Database**
   - Add database indexes for frequently queried fields
   - Use database connection pooling

2. **Frontend**
   - Enable gzip compression
   - Use CDN for static assets
   - Implement caching strategies

3. **Backend**
   - Use Redis for caching
   - Implement API rate limiting
   - Optimize database queries
