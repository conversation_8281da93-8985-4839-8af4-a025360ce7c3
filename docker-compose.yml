version: '3.8'

services:
  db:
    image: postgres:13
    volumes:
      - postgres_data:/var/lib/postgresql/data/
    environment:
      POSTGRES_DB: hyperlocal_dashboard
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"

  web:
    build: .
    command: >
      sh -c "python manage.py migrate &&
             python manage.py seed_stores &&
             gunicorn --bind 0.0.0.0:8000 backend.wsgi:application"
    volumes:
      - .:/app
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - DATABASE_URL=**************************************/hyperlocal_dashboard
      - ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
    depends_on:
      - db

volumes:
  postgres_data:
